'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Network, Home, Globe, BarChart3, Shield } from 'lucide-react'

export default function NetworkSystemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()

  const menuItems = [
    {
      id: 'home',
      label: '首页',
      icon: Home,
      href: '/network',
    },
    {
      id: 'management',
      label: '网络管理',
      icon: Network,
      href: '/network/management',
    },
    {
      id: 'topology',
      label: '网络拓扑',
      icon: Globe,
      href: '/network/topology',
    },
    {
      id: 'traffic',
      label: '流量监控',
      icon: BarChart3,
      href: '/network/traffic',
    },
    {
      id: 'security',
      label: '安全管理',
      icon: Shield,
      href: '/network/security',
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-rose-50 to-pink-100">
      {/* 系统头部 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
        <div className="w-full px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              {/* 系统Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Network className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                    🌐 网络管理系统
                  </h1>
                  <p className="text-sm text-gray-600">多物理网络管理与安全监控平台</p>
                </div>
              </div>

              {/* 导航菜单 */}
              <nav className="flex items-center space-x-1">
                {menuItems.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href
                  return (
                    <Link
                      key={item.id}
                      href={item.href}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${
                        isActive
                          ? 'bg-pink-100 text-pink-700 shadow-sm'
                          : 'text-gray-700 hover:text-pink-600 hover:bg-pink-50/50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </Link>
                  )
                })}
              </nav>
            </div>

            {/* 返回主平台 */}
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-600 hover:text-pink-600 transition-colors text-sm font-medium"
              >
                返回首页
              </a>
              <a
                href="/dashboard"
                className="bg-pink-600 text-white px-4 py-2 rounded-xl hover:bg-pink-700 transition-colors text-sm font-medium"
              >
                主控台
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="relative z-10">
        <div className="w-full px-6 py-8">
          {children}
        </div>
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-pink-400/20 to-rose-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-rose-400/20 to-pink-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}
