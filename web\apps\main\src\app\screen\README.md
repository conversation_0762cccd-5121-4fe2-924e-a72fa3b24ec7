# 数据大屏系统

## 概述
数据大屏系统是一个模块化的数据可视化展示平台，支持多种类型的大屏展示，包括政务数据总览、人口统计分析、经济运行监控等。

## 功能特性

### 1. 响应式布局
- 横向铺满设计，两边留少许空白
- 支持全屏显示模式
- 自适应不同屏幕尺寸

### 2. 模块化设计
- 每个大屏由多个独立模块组成
- 模块可独立开发和维护
- 支持模块的动态加载和配置

### 3. 实时数据更新
- 支持自动刷新功能
- 可配置刷新间隔
- 实时数据展示

## 目录结构

```
screen/
├── layout.tsx              # 大屏系统布局
├── page.tsx               # 大屏管理页面
├── view/[id]/page.tsx     # 大屏展示页面
├── components/
│   ├── GovernmentDataOverview.tsx  # 政务数据总览主组件
│   └── modules/           # 模块化组件目录
│       ├── DataSummaryModule.tsx    # 数据概览模块
│       ├── PopulationModule.tsx     # 人口统计模块
│       ├── EconomicModule.tsx       # 经济指标模块
│       ├── ServiceModule.tsx        # 政务服务模块
│       ├── GeographicModule.tsx     # 地理分布模块
│       └── TrendAnalysisModule.tsx  # 趋势分析模块
└── README.md              # 说明文档
```

## 使用方法

### 1. 访问大屏管理页面
访问 `/screen` 查看所有可用的大屏列表

### 2. 查看大屏
- 点击大屏卡片上的播放按钮或"查看"链接
- 访问 `/screen/view/{id}` 直接查看指定大屏

### 3. 全屏模式
- 在大屏页面点击全屏按钮
- 按 ESC 键退出全屏

### 4. 自动刷新
- 点击刷新按钮开启/关闭自动刷新
- 不同大屏有不同的刷新间隔

## 模块开发指南

### 1. 创建新模块
```tsx
'use client'

import { useState, useEffect } from 'react'

export default function NewModule() {
  const [data, setData] = useState(null)

  useEffect(() => {
    // 数据加载逻辑
    const loadData = () => {
      // 获取数据
    }
    
    loadData()
    const interval = setInterval(loadData, 30000) // 30秒刷新
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-4">
      {/* 模块内容 */}
    </div>
  )
}
```

### 2. 模块设计原则
- 使用统一的样式风格
- 支持响应式布局
- 包含加载状态和错误处理
- 数据更新要平滑过渡

### 3. 样式规范
- 背景：`bg-gradient-to-br from-{color}-800/20 to-{color}-900/20`
- 边框：`border border-{color}-700/30`
- 文字：主标题使用 `text-white`，副标题使用 `text-{color}-300`
- 图标：使用 lucide-react 图标库

## 大屏配置

### 1. 添加新大屏
在 `view/[id]/page.tsx` 的 `getScreenConfig` 函数中添加新配置：

```tsx
const configs = {
  'new-screen-id': {
    name: '新大屏名称',
    component: NewScreenComponent,
    refreshInterval: 30000
  }
}
```

### 2. 大屏布局
在主组件中使用 CSS Grid 进行布局：

```tsx
<div className="grid grid-cols-12 grid-rows-8 gap-4">
  <div className="col-span-6 row-span-4">
    <ModuleComponent />
  </div>
</div>
```

## 数据接口

### 1. 数据格式
所有模块的数据都应该遵循统一的格式规范，包含：
- 数值数据
- 趋势信息
- 时间戳
- 状态标识

### 2. 错误处理
- 网络错误时显示友好提示
- 数据异常时使用默认值
- 加载状态的视觉反馈

## 性能优化

### 1. 数据缓存
- 使用适当的缓存策略
- 避免频繁的数据请求
- 实现增量更新

### 2. 渲染优化
- 使用 React.memo 优化组件渲染
- 避免不必要的重新渲染
- 合理使用 useCallback 和 useMemo

## 部署说明

### 1. 环境要求
- Node.js 18+
- Next.js 13+
- React 18+

### 2. 构建命令
```bash
npm run build
npm run start
```

### 3. 环境变量
根据需要配置相关的环境变量，如 API 端点、刷新间隔等。
