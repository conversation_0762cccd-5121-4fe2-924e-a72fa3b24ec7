'use client'

import { useState, useEffect } from 'react'
import { MapPin, Navigation, Users, Building } from 'lucide-react'

interface GeographicData {
  districts: Array<{
    name: string
    population: number
    enterprises: number
    gdp: number
    coordinates: { x: number; y: number }
  }>
  heatmapData: Array<{
    x: number
    y: number
    intensity: number
    type: 'population' | 'economic' | 'service'
  }>
}

export default function GeographicModule() {
  const [data, setData] = useState<GeographicData>({
    districts: [],
    heatmapData: []
  })
  const [selectedDistrict, setSelectedDistrict] = useState<string | null>(null)

  useEffect(() => {
    const loadData = () => {
      setData({
        districts: [
          { name: '中心区', population: 345678, enterprises: 12456, gdp: 456.7, coordinates: { x: 50, y: 40 } },
          { name: '东城区', population: 234567, enterprises: 8934, gdp: 234.5, coordinates: { x: 70, y: 35 } },
          { name: '西城区', population: 198765, enterprises: 7823, gdp: 198.3, coordinates: { x: 30, y: 45 } },
          { name: '南区', population: 156789, enterprises: 5678, gdp: 167.8, coordinates: { x: 45, y: 70 } },
          { name: '北区', population: 187654, enterprises: 6789, gdp: 189.2, coordinates: { x: 55, y: 20 } },
          { name: '开发区', population: 123456, enterprises: 9876, gdp: 298.4, coordinates: { x: 80, y: 60 } }
        ],
        heatmapData: [
          { x: 25, y: 30, intensity: 0.8, type: 'population' },
          { x: 45, y: 25, intensity: 0.9, type: 'economic' },
          { x: 65, y: 40, intensity: 0.7, type: 'service' },
          { x: 35, y: 55, intensity: 0.6, type: 'population' },
          { x: 75, y: 65, intensity: 0.85, type: 'economic' },
          { x: 55, y: 75, intensity: 0.65, type: 'service' }
        ]
      })
    }

    loadData()
    const interval = setInterval(loadData, 60000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-cyan-800/20 to-cyan-900/20 backdrop-blur-sm rounded-2xl border border-cyan-700/30 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <MapPin className="w-5 h-5 text-cyan-400" />
        <h3 className="text-lg font-bold text-white">地理分布</h3>
      </div>

      <div className="grid grid-cols-3 gap-4 h-[calc(100%-60px)]">
        {/* 地图区域 */}
        <div className="col-span-2 bg-gray-800/30 rounded-lg p-3 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900/50 to-gray-800/50">
            {/* 模拟地图背景 */}
            <svg className="w-full h-full" viewBox="0 0 100 100">
              {/* 地图轮廓 */}
              <path
                d="M10,20 L90,20 L90,80 L10,80 Z"
                fill="none"
                stroke="rgba(99, 102, 241, 0.3)"
                strokeWidth="0.5"
              />
              
              {/* 区域分界线 */}
              <line x1="50" y1="20" x2="50" y2="80" stroke="rgba(99, 102, 241, 0.2)" strokeWidth="0.3" />
              <line x1="10" y1="50" x2="90" y2="50" stroke="rgba(99, 102, 241, 0.2)" strokeWidth="0.3" />
              
              {/* 热力图点 */}
              {data.heatmapData.map((point, index) => (
                <circle
                  key={index}
                  cx={point.x}
                  cy={point.y}
                  r={point.intensity * 3}
                  fill={
                    point.type === 'population' ? 'rgba(59, 130, 246, 0.6)' :
                    point.type === 'economic' ? 'rgba(34, 197, 94, 0.6)' :
                    'rgba(168, 85, 247, 0.6)'
                  }
                  className="animate-pulse"
                />
              ))}
              
              {/* 区域标记点 */}
              {data.districts.map((district, index) => (
                <g key={district.name}>
                  <circle
                    cx={district.coordinates.x}
                    cy={district.coordinates.y}
                    r="2"
                    fill="rgba(34, 211, 238, 0.8)"
                    className="cursor-pointer hover:r-3 transition-all"
                    onClick={() => setSelectedDistrict(district.name)}
                  />
                  <text
                    x={district.coordinates.x}
                    y={district.coordinates.y - 3}
                    fontSize="2"
                    fill="white"
                    textAnchor="middle"
                    className="pointer-events-none"
                  >
                    {district.name}
                  </text>
                </g>
              ))}
            </svg>
          </div>
        </div>

        {/* 数据面板 */}
        <div className="space-y-3">
          {/* 图例 */}
          <div className="bg-gray-800/30 rounded-lg p-3">
            <h4 className="text-sm font-semibold text-cyan-300 mb-2">图例</h4>
            <div className="space-y-1 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-gray-300">人口密度</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-gray-300">经济活跃度</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-gray-300">服务覆盖</span>
              </div>
            </div>
          </div>

          {/* 区域统计 */}
          <div className="bg-gray-800/30 rounded-lg p-3 flex-1 overflow-y-auto">
            <h4 className="text-sm font-semibold text-cyan-300 mb-2">区域统计</h4>
            <div className="space-y-2">
              {data.districts.map((district, index) => (
                <div
                  key={district.name}
                  className={`p-2 rounded cursor-pointer transition-all ${
                    selectedDistrict === district.name 
                      ? 'bg-cyan-600/30 border border-cyan-500/50' 
                      : 'bg-gray-700/30 hover:bg-gray-700/50'
                  }`}
                  onClick={() => setSelectedDistrict(district.name)}
                >
                  <div className="text-xs font-medium text-white mb-1">{district.name}</div>
                  <div className="grid grid-cols-2 gap-1 text-xs">
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3 text-blue-400" />
                      <span className="text-gray-300">{(district.population / 10000).toFixed(1)}万</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Building className="w-3 h-3 text-green-400" />
                      <span className="text-gray-300">{district.enterprises}</span>
                    </div>
                  </div>
                  <div className="text-xs text-cyan-300 mt-1">GDP: {district.gdp}亿</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
