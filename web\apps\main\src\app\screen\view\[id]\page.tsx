'use client'

import { useParams } from 'next/navigation'
import { useState, useEffect } from 'react'
import { ArrowLeft, Maximize2, Minimize2, RefreshCw, Settings } from 'lucide-react'
import Link from 'next/link'
import GovernmentDataOverview from '../../components/GovernmentDataOverview'

export default function ScreenViewPage() {
  const params = useParams()
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isAutoRefresh, setIsAutoRefresh] = useState(true)

  // 根据ID获取对应的大屏配置
  const getScreenConfig = (id: string) => {
    const configs = {
      '1': {
        name: '政务数据总览',
        component: GovernmentDataOverview,
        refreshInterval: 30000 // 30秒刷新一次
      },
      '2': {
        name: '人口统计分析',
        component: () => <div>人口统计分析大屏 - 开发中</div>,
        refreshInterval: 60000
      },
      '3': {
        name: '经济运行监控',
        component: () => <div>经济运行监控大屏 - 开发中</div>,
        refreshInterval: 30000
      },
      '4': {
        name: '环境质量监测',
        component: () => <div>环境质量监测大屏 - 开发中</div>,
        refreshInterval: 15000
      }
    }
    return configs[id as keyof typeof configs]
  }

  const screenConfig = getScreenConfig(params.id as string)
  const ScreenComponent = screenConfig?.component

  // 全屏切换
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    setIsFullscreen(!isFullscreen)
  }

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }
    
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // 自动刷新
  useEffect(() => {
    if (!isAutoRefresh || !screenConfig) return

    const interval = setInterval(() => {
      // 触发数据刷新
      window.location.reload()
    }, screenConfig.refreshInterval)

    return () => clearInterval(interval)
  }, [isAutoRefresh, screenConfig])

  if (!screenConfig) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">大屏不存在</h1>
          <Link 
            href="/screen" 
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            返回大屏列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen bg-gray-900 ${isFullscreen ? 'p-0' : 'px-4 py-2'}`}>
      {/* 顶部控制栏 */}
      {!isFullscreen && (
        <div className="flex items-center justify-between mb-4 bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-2">
          <div className="flex items-center space-x-4">
            <Link 
              href="/screen"
              className="text-gray-400 hover:text-white transition-colors flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回</span>
            </Link>
            <h1 className="text-lg font-semibold text-white">{screenConfig.name}</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsAutoRefresh(!isAutoRefresh)}
              className={`p-2 rounded-lg transition-colors ${
                isAutoRefresh 
                  ? 'bg-green-600 text-white' 
                  : 'bg-gray-700 text-gray-400 hover:text-white'
              }`}
              title={isAutoRefresh ? '关闭自动刷新' : '开启自动刷新'}
            >
              <RefreshCw className={`w-4 h-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="p-2 bg-gray-700 text-gray-400 hover:text-white rounded-lg transition-colors"
              title="全屏显示"
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      )}

      {/* 大屏内容 */}
      <div className={`${isFullscreen ? 'h-screen' : 'min-h-[calc(100vh-120px)]'}`}>
        {ScreenComponent && <ScreenComponent />}
      </div>
    </div>
  )
}
