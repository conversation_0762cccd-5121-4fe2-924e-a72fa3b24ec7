'use client'

import { 
  Activity, 
  TrendingUp, 
  TrendingDown,
  Zap,
  Download,
  Upload,
  Wifi,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON>Chart,
  RefreshCw,
  Filter,
  Calendar
} from 'lucide-react'

export default function TrafficMonitoringPage() {
  const trafficStats = [
    { 
      label: '总流量', 
      value: '2.5TB', 
      change: '+12%', 
      trend: 'up', 
      icon: Activity, 
      color: 'blue' 
    },
    { 
      label: '入站流量', 
      value: '1.2TB', 
      change: '+8%', 
      trend: 'up', 
      icon: Download, 
      color: 'green' 
    },
    { 
      label: '出站流量', 
      value: '1.3TB', 
      change: '+15%', 
      trend: 'up', 
      icon: Upload, 
      color: 'purple' 
    },
    { 
      label: '峰值带宽', 
      value: '850Mbps', 
      change: '-5%', 
      trend: 'down', 
      icon: Zap, 
      color: 'orange' 
    }
  ]

  const networkInterfaces = [
    {
      id: 1,
      name: '政务内网接口',
      interface: 'eth0',
      status: 'active',
      bandwidth: '1Gbps',
      utilization: 45,
      inbound: '125MB/s',
      outbound: '89MB/s',
      packets: '1.2M/s',
      errors: 0
    },
    {
      id: 2,
      name: '公安专网接口',
      interface: 'eth1',
      status: 'active',
      bandwidth: '500Mbps',
      utilization: 72,
      inbound: '180MB/s',
      outbound: '156MB/s',
      packets: '890K/s',
      errors: 2
    },
    {
      id: 3,
      name: '互联网接口',
      interface: 'eth2',
      status: 'warning',
      bandwidth: '100Mbps',
      utilization: 88,
      inbound: '44MB/s',
      outbound: '42MB/s',
      packets: '320K/s',
      errors: 15
    },
    {
      id: 4,
      name: '移动信息网接口',
      interface: 'wlan0',
      status: 'active',
      bandwidth: '200Mbps',
      utilization: 35,
      inbound: '35MB/s',
      outbound: '28MB/s',
      packets: '180K/s',
      errors: 1
    }
  ]

  const topApplications = [
    { name: 'Web浏览', traffic: '450MB', percentage: 35, color: 'bg-blue-500' },
    { name: '文件传输', traffic: '320MB', percentage: 25, color: 'bg-green-500' },
    { name: '视频会议', traffic: '280MB', percentage: 22, color: 'bg-purple-500' },
    { name: '邮件服务', traffic: '150MB', percentage: 12, color: 'bg-orange-500' },
    { name: '其他', traffic: '80MB', percentage: 6, color: 'bg-gray-500' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      default: return <CheckCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'bg-red-500'
    if (utilization >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? 
      <TrendingUp className="w-4 h-4 text-green-500" /> : 
      <TrendingDown className="w-4 h-4 text-red-500" />
  }

  return (
    <div className="w-full">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">流量监控</h1>
        <p className="text-xl text-gray-600">实时监控网络流量状态和带宽使用情况</p>
      </div>

      {/* 流量统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {trafficStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex items-center space-x-1">
                  {getTrendIcon(stat.trend)}
                  <span className={`text-sm font-medium ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作工具栏 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-pink-600 text-white px-6 py-3 rounded-xl hover:bg-pink-700 transition-colors flex items-center space-x-2 font-medium">
            <RefreshCw className="w-5 h-5" />
            <span>刷新数据</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Filter className="w-5 h-5" />
            <span>筛选</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Calendar className="w-5 h-5" />
            <span>时间范围</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* 实时流量图表 */}
        <div className="lg:col-span-2 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">实时流量趋势</h3>
            <div className="flex items-center space-x-2">
              <LineChart className="w-5 h-5 text-gray-500" />
              <span className="text-sm text-gray-600">过去24小时</span>
            </div>
          </div>
          
          {/* 模拟图表区域 */}
          <div className="h-64 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">流量趋势图表</p>
              <p className="text-sm">实时显示网络流量变化</p>
            </div>
          </div>
        </div>

        {/* 应用流量分布 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">应用流量分布</h3>
            <PieChart className="w-5 h-5 text-gray-500" />
          </div>
          
          <div className="space-y-4">
            {topApplications.map((app, index) => (
              <div key={app.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${app.color}`}></div>
                  <span className="text-sm font-medium text-gray-700">{app.name}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-gray-900">{app.traffic}</p>
                  <p className="text-xs text-gray-500">{app.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 网络接口监控 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">网络接口监控</h3>
        
        <div className="grid grid-cols-1 gap-6">
          {networkInterfaces.map((iface) => (
            <div
              key={iface.id}
              className="bg-gray-50/50 rounded-xl p-6 hover:bg-gray-100/50 transition-colors"
            >
              {/* 接口头部信息 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center">
                    <Wifi className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">{iface.name}</h4>
                    <p className="text-sm text-gray-600">{iface.interface} - {iface.bandwidth}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(iface.status)}
                  <span className="text-sm font-medium text-gray-600">
                    {iface.errors > 0 ? `${iface.errors} 错误` : '正常'}
                  </span>
                </div>
              </div>

              {/* 流量统计 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">入站流量</p>
                  <p className="text-lg font-bold text-green-600">{iface.inbound}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">出站流量</p>
                  <p className="text-lg font-bold text-blue-600">{iface.outbound}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">数据包</p>
                  <p className="text-lg font-bold text-purple-600">{iface.packets}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">利用率</p>
                  <p className="text-lg font-bold text-gray-900">{iface.utilization}%</p>
                </div>
              </div>

              {/* 带宽利用率进度条 */}
              <div>
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-gray-600">带宽利用率</span>
                  <span className="font-medium">{iface.utilization}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${getUtilizationColor(iface.utilization)}`}
                    style={{ width: `${iface.utilization}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
