'use client'

import { useState, useEffect } from 'react'
import DataSummaryModule from './modules/DataSummaryModule'
import PopulationModule from './modules/PopulationModule'
import EconomicModule from './modules/EconomicModule'
import ServiceModule from './modules/ServiceModule'
import GeographicModule from './modules/GeographicModule'
import TrendAnalysisModule from './modules/TrendAnalysisModule'

export default function GovernmentDataOverview() {
  const [currentTime, setCurrentTime] = useState(new Date())

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white overflow-hidden">
      {/* 顶部标题栏 */}
      <div className="bg-gradient-to-r from-blue-800/30 to-cyan-800/30 backdrop-blur-sm border-b border-blue-500/20 px-8 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              政务数据总览大屏
            </h1>
            <p className="text-blue-200 mt-1">Government Data Overview Dashboard</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-mono text-blue-300">
              {currentTime.toLocaleTimeString('zh-CN', { hour12: false })}
            </div>
            <div className="text-sm text-blue-200">
              {currentTime.toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="p-6 h-[calc(100%-100px)] grid grid-cols-12 grid-rows-8 gap-4">
        {/* 数据概览模块 - 顶部横跨 */}
        <div className="col-span-12 row-span-2">
          <DataSummaryModule />
        </div>

        {/* 人口统计模块 */}
        <div className="col-span-4 row-span-3">
          <PopulationModule />
        </div>

        {/* 经济指标模块 */}
        <div className="col-span-4 row-span-3">
          <EconomicModule />
        </div>

        {/* 政务服务模块 */}
        <div className="col-span-4 row-span-3">
          <ServiceModule />
        </div>

        {/* 地理分布模块 */}
        <div className="col-span-8 row-span-3">
          <GeographicModule />
        </div>

        {/* 趋势分析模块 */}
        <div className="col-span-4 row-span-3">
          <TrendAnalysisModule />
        </div>
      </div>
    </div>
  )
}
