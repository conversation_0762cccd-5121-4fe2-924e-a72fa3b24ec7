'use client'

import Link from 'next/link'
import { 
  Network, 
  Router, 
  Wifi, 
  Shield,
  Activity,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Plus,
  ArrowRight,
  BarChart3,
  Clock,
  Globe,
  Lock
} from 'lucide-react'

export default function NetworkSystemPage() {
  const networks = [
    {
      id: 1,
      name: '政务内网',
      type: '内部网络',
      subnet: '192.168.1.0/24',
      status: 'active',
      devices: 156,
      bandwidth: '1Gbps',
      utilization: 45,
      security: 'high',
      location: '主机房'
    },
    {
      id: 2,
      name: '政务外网',
      type: '外部网络',
      subnet: '10.0.1.0/24',
      status: 'active',
      devices: 89,
      bandwidth: '500Mbps',
      utilization: 72,
      security: 'medium',
      location: '主机房'
    },
    {
      id: 3,
      name: '专网接入',
      type: '专用网络',
      subnet: '172.16.1.0/24',
      status: 'warning',
      devices: 24,
      bandwidth: '100Mbps',
      utilization: 88,
      security: 'high',
      location: '分机房A'
    },
    {
      id: 4,
      name: '备份网络',
      type: '备份网络',
      subnet: '192.168.100.0/24',
      status: 'offline',
      devices: 12,
      bandwidth: '100Mbps',
      utilization: 0,
      security: 'medium',
      location: '分机房B'
    }
  ]

  const quickStats = [
    { label: '网络数量', value: '8', trend: '+1', icon: Network, color: 'pink' },
    { label: '在线设备', value: '256', trend: '+12', icon: Router, color: 'blue' },
    { label: '总带宽', value: '2.5Gbps', trend: '+500M', icon: Activity, color: 'green' },
    { label: '安全事件', value: '2', trend: '-3', icon: Shield, color: 'orange' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'offline': return <XCircle className="w-5 h-5 text-red-500" />
      default: return <XCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常'
      case 'warning': return '告警'
      case 'offline': return '离线'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700'
      case 'warning': return 'bg-yellow-100 text-yellow-700'
      case 'offline': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getSecurityIcon = (security: string) => {
    switch (security) {
      case 'high': return <Shield className="w-4 h-4 text-green-500" />
      case 'medium': return <Shield className="w-4 h-4 text-yellow-500" />
      case 'low': return <Shield className="w-4 h-4 text-red-500" />
      default: return <Shield className="w-4 h-4 text-gray-500" />
    }
  }

  const getSecurityText = (security: string) => {
    switch (security) {
      case 'high': return '高'
      case 'medium': return '中'
      case 'low': return '低'
      default: return '未知'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'bg-red-500'
    if (utilization >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="w-full">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">网络管理首页</h1>
        <p className="text-xl text-gray-600">管理多物理网络，监控网络拓扑和流量，确保网络安全</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-pink-600 text-white px-6 py-3 rounded-xl hover:bg-pink-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加网络</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <BarChart3 className="w-5 h-5" />
            <span>网络拓扑</span>
          </button>
        </div>
      </div>

      {/* 网络列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {networks.map((network, index) => (
          <div
            key={network.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{network.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(network.status)}`}>
                      {getStatusText(network.status)}
                    </span>
                    {getStatusIcon(network.status)}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <span className="bg-gray-100 px-2 py-1 rounded-full">{network.type}</span>
                    <span>{network.subnet}</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">{network.location}</span>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Network className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* 网络信息 */}
              <div className="grid grid-cols-2 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">连接设备</p>
                  <p className="text-lg font-bold text-gray-900">{network.devices}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">带宽</p>
                  <p className="text-sm font-medium text-gray-900">{network.bandwidth}</p>
                </div>
              </div>

              {/* 带宽利用率 */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <div className="flex items-center space-x-1">
                    <Activity className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">带宽利用率</span>
                  </div>
                  <span className="font-medium">{network.utilization}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-500 ${getUtilizationColor(network.utilization)}`}
                    style={{ width: `${network.utilization}%` }}
                  ></div>
                </div>
              </div>

              {/* 安全级别 */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-1">
                  {getSecurityIcon(network.security)}
                  <span>安全级别: {getSecurityText(network.security)}</span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="text-pink-600 hover:text-pink-700 font-medium text-sm transition-colors">
                    配置
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    流量分析
                  </button>
                </div>
                <Link
                  href={`/network/detail/${network.id}`}
                  className="text-pink-600 hover:text-pink-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>详情</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
