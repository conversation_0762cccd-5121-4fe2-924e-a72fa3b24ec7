'use client'

import { 
  Network, 
  Router, 
  Wifi, 
  Shield,
  Server,
  Globe,
  Zap,
  Activity,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Maximize2,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react'

export default function NetworkTopologyPage() {
  const topologyNodes = [
    {
      id: 'core-switch',
      name: '核心交换机',
      type: 'switch',
      status: 'active',
      ip: '***********',
      position: { x: 50, y: 20 },
      connections: ['firewall', 'router-1', 'router-2']
    },
    {
      id: 'firewall',
      name: '防火墙',
      type: 'firewall',
      status: 'active',
      ip: '*************',
      position: { x: 20, y: 50 },
      connections: ['internet']
    },
    {
      id: 'router-1',
      name: '政务网路由器',
      type: 'router',
      status: 'active',
      ip: '************',
      position: { x: 80, y: 50 },
      connections: ['gov-switch']
    },
    {
      id: 'router-2',
      name: '公安网路由器',
      type: 'router',
      status: 'warning',
      ip: '*********',
      position: { x: 50, y: 80 },
      connections: ['police-switch']
    },
    {
      id: 'internet',
      name: '互联网',
      type: 'internet',
      status: 'active',
      ip: '外网',
      position: { x: 20, y: 80 }
    },
    {
      id: 'gov-switch',
      name: '政务交换机',
      type: 'switch',
      status: 'active',
      ip: '************',
      position: { x: 80, y: 80 }
    },
    {
      id: 'police-switch',
      name: '公安交换机',
      type: 'switch',
      status: 'active',
      ip: '*********0',
      position: { x: 20, y: 20 }
    }
  ]

  const networkStats = [
    { label: '网络节点', value: '12', icon: Network, color: 'blue' },
    { label: '活跃连接', value: '156', icon: Zap, color: 'green' },
    { label: '数据流量', value: '2.5GB/s', icon: Activity, color: 'purple' },
    { label: '告警数量', value: '3', icon: AlertTriangle, color: 'orange' }
  ]

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'switch': return <Network className="w-8 h-8" />
      case 'router': return <Router className="w-8 h-8" />
      case 'firewall': return <Shield className="w-8 h-8" />
      case 'server': return <Server className="w-8 h-8" />
      case 'internet': return <Globe className="w-8 h-8" />
      case 'wifi': return <Wifi className="w-8 h-8" />
      default: return <Network className="w-8 h-8" />
    }
  }

  const getNodeColor = (type: string, status: string) => {
    const baseColors = {
      switch: 'from-blue-500 to-blue-600',
      router: 'from-green-500 to-green-600',
      firewall: 'from-red-500 to-red-600',
      server: 'from-purple-500 to-purple-600',
      internet: 'from-orange-500 to-orange-600',
      wifi: 'from-indigo-500 to-indigo-600'
    }
    
    if (status === 'warning') return 'from-yellow-500 to-yellow-600'
    if (status === 'offline') return 'from-gray-400 to-gray-500'
    
    return baseColors[type as keyof typeof baseColors] || 'from-gray-500 to-gray-600'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'offline': return <XCircle className="w-4 h-4 text-red-500" />
      default: return <XCircle className="w-4 h-4 text-gray-500" />
    }
  }

  return (
    <div className="w-full">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">网络拓扑</h1>
        <p className="text-xl text-gray-600">可视化展示网络架构和设备连接关系</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {networkStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作工具栏 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-pink-600 text-white px-6 py-3 rounded-xl hover:bg-pink-700 transition-colors flex items-center space-x-2 font-medium">
            <RefreshCw className="w-5 h-5" />
            <span>刷新拓扑</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Maximize2 className="w-5 h-5" />
            <span>全屏查看</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Download className="w-5 h-5" />
            <span>导出图片</span>
          </button>
        </div>
        <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
          <Settings className="w-5 h-5" />
          <span>拓扑设置</span>
        </button>
      </div>

      {/* 网络拓扑图 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8 mb-8">
        <div className="relative w-full h-96 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden">
          {/* 网格背景 */}
          <div className="absolute inset-0 opacity-20">
            <svg width="100%" height="100%">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          {/* 连接线 */}
          <svg className="absolute inset-0 w-full h-full">
            {topologyNodes.map((node) => 
              node.connections?.map((targetId) => {
                const target = topologyNodes.find(n => n.id === targetId)
                if (!target) return null
                
                const x1 = (node.position.x / 100) * 100 + '%'
                const y1 = (node.position.y / 100) * 100 + '%'
                const x2 = (target.position.x / 100) * 100 + '%'
                const y2 = (target.position.y / 100) * 100 + '%'
                
                return (
                  <line
                    key={`${node.id}-${targetId}`}
                    x1={x1}
                    y1={y1}
                    x2={x2}
                    y2={y2}
                    stroke="#6b7280"
                    strokeWidth="2"
                    strokeDasharray="5,5"
                    className="animate-pulse"
                  />
                )
              })
            )}
          </svg>

          {/* 网络节点 */}
          {topologyNodes.map((node) => (
            <div
              key={node.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
              style={{
                left: `${node.position.x}%`,
                top: `${node.position.y}%`
              }}
            >
              {/* 节点图标 */}
              <div className={`w-16 h-16 bg-gradient-to-br ${getNodeColor(node.type, node.status)} rounded-xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}>
                {getNodeIcon(node.type)}
              </div>
              
              {/* 状态指示器 */}
              <div className="absolute -top-1 -right-1">
                {getStatusIcon(node.status)}
              </div>
              
              {/* 节点信息卡片 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg shadow-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 whitespace-nowrap">
                <h4 className="font-semibold text-gray-900 text-sm">{node.name}</h4>
                <p className="text-xs text-gray-600">{node.ip}</p>
                <p className="text-xs text-gray-500 capitalize">{node.type}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 网络设备列表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">网络设备详情</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {topologyNodes.map((node) => (
            <div
              key={node.id}
              className="bg-gray-50/50 rounded-xl p-4 hover:bg-gray-100/50 transition-colors"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className={`w-10 h-10 bg-gradient-to-br ${getNodeColor(node.type, node.status)} rounded-lg flex items-center justify-center text-white`}>
                  {getNodeIcon(node.type)}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 text-sm">{node.name}</h4>
                  <p className="text-xs text-gray-600">{node.ip}</p>
                </div>
                {getStatusIcon(node.status)}
              </div>
              <div className="text-xs text-gray-500">
                <p>类型: {node.type}</p>
                <p>连接数: {node.connections?.length || 0}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
