'use client'

import { 
  Shield, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Zap,
  Activity,
  Users,
  Globe,
  Ban,
  Settings,
  RefreshCw,
  Download,
  Filter,
  Search
} from 'lucide-react'

export default function SecurityManagementPage() {
  const securityStats = [
    { 
      label: '安全事件', 
      value: '12', 
      change: '-8', 
      trend: 'down', 
      icon: AlertTriangle, 
      color: 'red' 
    },
    { 
      label: '防护规则', 
      value: '156', 
      change: '+5', 
      trend: 'up', 
      icon: Shield, 
      color: 'green' 
    },
    { 
      label: '阻断攻击', 
      value: '89', 
      change: '+12', 
      trend: 'up', 
      icon: Ban, 
      color: 'orange' 
    },
    { 
      label: '在线用户', 
      value: '245', 
      change: '+18', 
      trend: 'up', 
      icon: Users, 
      color: 'blue' 
    }
  ]

  const securityEvents = [
    {
      id: 1,
      type: 'intrusion',
      severity: 'high',
      title: '检测到入侵尝试',
      description: '来自 ************* 的异常登录尝试',
      source: '*************',
      target: '政务内网',
      time: '2024-01-15 14:30:25',
      status: 'blocked'
    },
    {
      id: 2,
      type: 'malware',
      severity: 'medium',
      title: '恶意软件检测',
      description: '在文件传输中发现可疑文件',
      source: '外部网络',
      target: '文件服务器',
      time: '2024-01-15 13:45:12',
      status: 'quarantined'
    },
    {
      id: 3,
      type: 'ddos',
      severity: 'high',
      title: 'DDoS攻击检测',
      description: '检测到大量异常流量',
      source: '多个IP',
      target: 'Web服务器',
      time: '2024-01-15 12:20:08',
      status: 'mitigated'
    },
    {
      id: 4,
      type: 'unauthorized',
      severity: 'low',
      title: '未授权访问尝试',
      description: '尝试访问受限资源',
      source: '*********',
      target: '数据库服务器',
      time: '2024-01-15 11:15:33',
      status: 'blocked'
    }
  ]

  const firewallRules = [
    {
      id: 1,
      name: '政务网访问控制',
      source: '***********/24',
      destination: '********/24',
      port: '80,443',
      action: 'allow',
      status: 'active',
      priority: 1
    },
    {
      id: 2,
      name: '外网访问限制',
      source: 'any',
      destination: '***********/24',
      port: '22',
      action: 'deny',
      status: 'active',
      priority: 2
    },
    {
      id: 3,
      name: 'VPN访问规则',
      source: '*********/24',
      destination: 'any',
      port: 'any',
      action: 'allow',
      status: 'active',
      priority: 3
    },
    {
      id: 4,
      name: '恶意IP阻断',
      source: '*************',
      destination: 'any',
      port: 'any',
      action: 'deny',
      status: 'active',
      priority: 0
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-700 border-blue-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="w-4 h-4 text-red-500" />
      case 'medium': return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'low': return <AlertTriangle className="w-4 h-4 text-blue-500" />
      default: return <AlertTriangle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'blocked': return <Ban className="w-4 h-4 text-red-500" />
      case 'quarantined': return <Lock className="w-4 h-4 text-orange-500" />
      case 'mitigated': return <Shield className="w-4 h-4 text-green-500" />
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />
      default: return <XCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getActionColor = (action: string) => {
    return action === 'allow' ? 'text-green-600' : 'text-red-600'
  }

  return (
    <div className="w-full">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">安全管理</h1>
        <p className="text-xl text-gray-600">网络安全监控、威胁检测和防护策略管理</p>
      </div>

      {/* 安全统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {securityStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <span className={`text-sm font-medium ${stat.trend === 'up' ? 'text-red-600' : 'text-green-600'}`}>
                    {stat.change > 0 ? '+' : ''}{stat.change}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作工具栏 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-pink-600 text-white px-6 py-3 rounded-xl hover:bg-pink-700 transition-colors flex items-center space-x-2 font-medium">
            <RefreshCw className="w-5 h-5" />
            <span>刷新状态</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Filter className="w-5 h-5" />
            <span>筛选事件</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Download className="w-5 h-5" />
            <span>导出报告</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* 安全事件 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">最新安全事件</h3>
            <div className="flex items-center space-x-2">
              <Search className="w-5 h-5 text-gray-500" />
              <input
                type="text"
                placeholder="搜索事件..."
                className="text-sm border border-gray-200 rounded-lg px-3 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
            </div>
          </div>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {securityEvents.map((event) => (
              <div
                key={event.id}
                className="border border-gray-200 rounded-xl p-4 hover:bg-gray-50/50 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getSeverityIcon(event.severity)}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                      {event.severity.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(event.status)}
                    <span className="text-xs text-gray-500">{event.status}</span>
                  </div>
                </div>
                
                <h4 className="font-semibold text-gray-900 mb-2">{event.title}</h4>
                <p className="text-sm text-gray-600 mb-3">{event.description}</p>
                
                <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                  <div>源: {event.source}</div>
                  <div>目标: {event.target}</div>
                  <div className="col-span-2">时间: {event.time}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 防火墙规则 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">防火墙规则</h3>
            <button className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 transition-colors text-sm font-medium">
              添加规则
            </button>
          </div>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {firewallRules.map((rule) => (
              <div
                key={rule.id}
                className="border border-gray-200 rounded-xl p-4 hover:bg-gray-50/50 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">{rule.name}</h4>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(rule.status)}
                    <span className={`text-sm font-medium ${getActionColor(rule.action)}`}>
                      {rule.action.toUpperCase()}
                    </span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 gap-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>源地址:</span>
                    <span className="font-mono">{rule.source}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>目标地址:</span>
                    <span className="font-mono">{rule.destination}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>端口:</span>
                    <span className="font-mono">{rule.port}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>优先级:</span>
                    <span>{rule.priority}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-end space-x-2 mt-3">
                  <button className="text-blue-600 hover:text-blue-700 text-sm">编辑</button>
                  <button className="text-red-600 hover:text-red-700 text-sm">删除</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 安全策略配置 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">安全策略配置</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gray-50/50 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Shield className="w-8 h-8 text-green-500" />
              <div>
                <h4 className="font-semibold text-gray-900">入侵检测</h4>
                <p className="text-sm text-gray-600">实时监控网络入侵</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">状态</span>
              <span className="text-sm font-medium text-green-600">已启用</span>
            </div>
          </div>
          
          <div className="bg-gray-50/50 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Ban className="w-8 h-8 text-red-500" />
              <div>
                <h4 className="font-semibold text-gray-900">DDoS防护</h4>
                <p className="text-sm text-gray-600">防御分布式拒绝服务攻击</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">状态</span>
              <span className="text-sm font-medium text-green-600">已启用</span>
            </div>
          </div>
          
          <div className="bg-gray-50/50 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Lock className="w-8 h-8 text-blue-500" />
              <div>
                <h4 className="font-semibold text-gray-900">访问控制</h4>
                <p className="text-sm text-gray-600">基于角色的访问控制</p>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">状态</span>
              <span className="text-sm font-medium text-green-600">已启用</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
