'use client'

import { useState, useEffect } from 'react'
import { Users, <PERSON>r<PERSON><PERSON><PERSON>, <PERSON>, GraduationCap } from 'lucide-react'

interface PopulationData {
  ageGroups: Array<{
    name: string
    value: number
    percentage: number
  }>
  genderRatio: {
    male: number
    female: number
  }
  educationLevel: Array<{
    level: string
    count: number
    percentage: number
  }>
}

export default function PopulationModule() {
  const [data, setData] = useState<PopulationData>({
    ageGroups: [],
    genderRatio: { male: 0, female: 0 },
    educationLevel: []
  })

  useEffect(() => {
    const loadData = () => {
      setData({
        ageGroups: [
          { name: '0-18岁', value: 234567, percentage: 18.8 },
          { name: '19-35岁', value: 456789, percentage: 36.7 },
          { name: '36-60岁', value: 423456, percentage: 34.0 },
          { name: '60岁以上', value: 130866, percentage: 10.5 }
        ],
        genderRatio: {
          male: 52.3,
          female: 47.7
        },
        educationLevel: [
          { level: '本科及以上', count: 345678, percentage: 27.8 },
          { level: '专科', count: 234567, percentage: 18.8 },
          { level: '高中', count: 456789, percentage: 36.7 },
          { level: '初中及以下', count: 208644, percentage: 16.7 }
        ]
      })
    }

    loadData()
    const interval = setInterval(loadData, 60000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-blue-800/20 to-blue-900/20 backdrop-blur-sm rounded-2xl border border-blue-700/30 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <Users className="w-5 h-5 text-blue-400" />
        <h3 className="text-lg font-bold text-white">人口统计</h3>
      </div>

      <div className="space-y-4 h-[calc(100%-60px)] overflow-y-auto">
        {/* 年龄分布 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-blue-300 mb-3">年龄分布</h4>
          <div className="space-y-2">
            {data.ageGroups.map((group, index) => (
              <div key={group.name} className="flex items-center justify-between">
                <span className="text-xs text-gray-300">{group.name}</span>
                <div className="flex items-center space-x-2 flex-1 mx-3">
                  <div className="flex-1 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${group.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-blue-300 w-8">{group.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 性别比例 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-blue-300 mb-3">性别比例</h4>
          <div className="flex items-center justify-center space-x-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-2">
                <UserCheck className="w-8 h-8 text-white" />
              </div>
              <div className="text-lg font-bold text-white">{data.genderRatio.male}%</div>
              <div className="text-xs text-gray-400">男性</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-8 h-8 text-white" />
              </div>
              <div className="text-lg font-bold text-white">{data.genderRatio.female}%</div>
              <div className="text-xs text-gray-400">女性</div>
            </div>
          </div>
        </div>

        {/* 教育水平 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-blue-300 mb-3">教育水平</h4>
          <div className="space-y-2">
            {data.educationLevel.map((edu, index) => (
              <div key={edu.level} className="flex items-center justify-between text-xs">
                <span className="text-gray-300">{edu.level}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-300">{edu.count.toLocaleString()}</span>
                  <span className="text-gray-400">({edu.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
