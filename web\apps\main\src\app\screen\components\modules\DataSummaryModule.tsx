'use client'

import { useState, useEffect } from 'react'
import { Users, Building2, FileText, TrendingUp, Activity, Database } from 'lucide-react'

interface SummaryData {
  totalPopulation: number
  totalEnterprises: number
  totalServices: number
  gdpGrowth: number
  onlineServices: number
  dataVolume: string
}

export default function DataSummaryModule() {
  const [data, setData] = useState<SummaryData>({
    totalPopulation: 0,
    totalEnterprises: 0,
    totalServices: 0,
    gdpGrowth: 0,
    onlineServices: 0,
    dataVolume: '0TB'
  })

  // 模拟数据加载
  useEffect(() => {
    const loadData = () => {
      setData({
        totalPopulation: 1245678,
        totalEnterprises: 45623,
        totalServices: 156,
        gdpGrowth: 8.5,
        onlineServices: 142,
        dataVolume: '2.4TB'
      })
    }

    loadData()
    const interval = setInterval(loadData, 30000) // 30秒更新一次
    return () => clearInterval(interval)
  }, [])

  const summaryItems = [
    {
      title: '总人口',
      value: data.totalPopulation.toLocaleString(),
      unit: '人',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      trend: '+2.3%'
    },
    {
      title: '注册企业',
      value: data.totalEnterprises.toLocaleString(),
      unit: '家',
      icon: Building2,
      color: 'from-green-500 to-green-600',
      trend: '+5.7%'
    },
    {
      title: '政务服务',
      value: data.totalServices.toString(),
      unit: '项',
      icon: FileText,
      color: 'from-purple-500 to-purple-600',
      trend: '+12'
    },
    {
      title: 'GDP增长',
      value: data.gdpGrowth.toString(),
      unit: '%',
      icon: TrendingUp,
      color: 'from-orange-500 to-orange-600',
      trend: '+0.8%'
    },
    {
      title: '在线服务',
      value: data.onlineServices.toString(),
      unit: '项',
      icon: Activity,
      color: 'from-cyan-500 to-cyan-600',
      trend: '+8'
    },
    {
      title: '数据总量',
      value: data.dataVolume,
      unit: '',
      icon: Database,
      color: 'from-pink-500 to-pink-600',
      trend: '+15%'
    }
  ]

  return (
    <div className="h-full bg-gradient-to-br from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">数据概览</h2>
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span>实时更新</span>
        </div>
      </div>

      <div className="grid grid-cols-6 gap-6 h-[calc(100%-80px)]">
        {summaryItems.map((item, index) => {
          const Icon = item.icon
          return (
            <div
              key={item.title}
              className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-xl p-4 border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between mb-3">
                <div className={`w-10 h-10 bg-gradient-to-br ${item.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                  <Icon className="w-5 h-5 text-white" />
                </div>
                <div className="text-xs text-green-400 font-medium">
                  {item.trend}
                </div>
              </div>
              
              <div className="space-y-1">
                <div className="text-2xl font-bold text-white">
                  {item.value}
                  <span className="text-sm text-gray-400 ml-1">{item.unit}</span>
                </div>
                <div className="text-sm text-gray-400">{item.title}</div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
