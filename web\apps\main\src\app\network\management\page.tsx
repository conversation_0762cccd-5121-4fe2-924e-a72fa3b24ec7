'use client'

import { 
  Network, 
  Router, 
  Wifi, 
  Shield,
  Activity,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Plus,
  Settings,
  Edit,
  Trash2,
  Eye,
  Server,
  Globe
} from 'lucide-react'

export default function NetworkManagementPage() {
  const networks = [
    {
      id: 1,
      name: '政务内网',
      type: '内部网络',
      subnet: '***********/24',
      status: 'active',
      devices: 156,
      bandwidth: '1Gbps',
      utilization: 45,
      security: 'high',
      location: '主机房',
      description: '政务内部办公网络，连接各部门办公设备'
    },
    {
      id: 2,
      name: '公安专网',
      type: '专用网络',
      subnet: '*********/24',
      status: 'active',
      devices: 89,
      bandwidth: '500Mbps',
      utilization: 72,
      security: 'high',
      location: '公安机房',
      description: '公安系统专用网络，高安全级别'
    },
    {
      id: 3,
      name: '互联网接入',
      type: '外部网络',
      subnet: '**********/24',
      status: 'warning',
      devices: 24,
      bandwidth: '100Mbps',
      utilization: 88,
      security: 'medium',
      location: '边界机房',
      description: '互联网出口网络，提供外网访问'
    },
    {
      id: 4,
      name: '移动信息网',
      type: '移动网络',
      subnet: '*************/24',
      status: 'active',
      devices: 45,
      bandwidth: '200Mbps',
      utilization: 35,
      security: 'medium',
      location: '移动基站',
      description: '移动办公网络，支持移动设备接入'
    },
    {
      id: 5,
      name: 'VPN专网',
      type: 'VPN网络',
      subnet: '*********/24',
      status: 'offline',
      devices: 12,
      bandwidth: '50Mbps',
      utilization: 0,
      security: 'high',
      location: 'VPN网关',
      description: 'VPN远程接入网络，支持远程办公'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'offline': return <XCircle className="w-5 h-5 text-red-500" />
      default: return <XCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常'
      case 'warning': return '告警'
      case 'offline': return '离线'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700'
      case 'warning': return 'bg-yellow-100 text-yellow-700'
      case 'offline': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getSecurityIcon = (security: string) => {
    switch (security) {
      case 'high': return <Shield className="w-4 h-4 text-green-500" />
      case 'medium': return <Shield className="w-4 h-4 text-yellow-500" />
      case 'low': return <Shield className="w-4 h-4 text-red-500" />
      default: return <Shield className="w-4 h-4 text-gray-500" />
    }
  }

  const getSecurityText = (security: string) => {
    switch (security) {
      case 'high': return '高'
      case 'medium': return '中'
      case 'low': return '低'
      default: return '未知'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'bg-red-500'
    if (utilization >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getNetworkIcon = (type: string) => {
    switch (type) {
      case '内部网络': return <Network className="w-6 h-6 text-blue-500" />
      case '专用网络': return <Shield className="w-6 h-6 text-green-500" />
      case '外部网络': return <Globe className="w-6 h-6 text-orange-500" />
      case '移动网络': return <Wifi className="w-6 h-6 text-purple-500" />
      case 'VPN网络': return <Server className="w-6 h-6 text-indigo-500" />
      default: return <Router className="w-6 h-6 text-gray-500" />
    }
  }

  return (
    <div className="w-full">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">网络管理</h1>
        <p className="text-xl text-gray-600">管理和配置多物理隔离网络，监控网络状态和性能</p>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-pink-600 text-white px-6 py-3 rounded-xl hover:bg-pink-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加网络</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Settings className="w-5 h-5" />
            <span>批量配置</span>
          </button>
        </div>
        <div className="text-sm text-gray-600">
          共 {networks.length} 个网络
        </div>
      </div>

      {/* 网络列表 */}
      <div className="grid grid-cols-1 gap-6">
        {networks.map((network) => (
          <div
            key={network.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
          >
            <div className="p-6">
              {/* 头部信息 */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    {getNetworkIcon(network.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{network.name}</h3>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(network.status)}`}>
                        {getStatusText(network.status)}
                      </span>
                      {getStatusIcon(network.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <span className="bg-gray-100 px-3 py-1 rounded-full">{network.type}</span>
                      <span className="font-mono">{network.subnet}</span>
                      <span className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full">{network.location}</span>
                    </div>
                    <p className="text-gray-600 text-sm">{network.description}</p>
                  </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                    <Edit className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* 网络统计信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">连接设备</p>
                  <p className="text-lg font-bold text-gray-900">{network.devices}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">带宽</p>
                  <p className="text-sm font-medium text-gray-900">{network.bandwidth}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">利用率</p>
                  <p className="text-sm font-medium text-gray-900">{network.utilization}%</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    {getSecurityIcon(network.security)}
                    <span className="text-xs text-gray-600">安全级别: {getSecurityText(network.security)}</span>
                  </div>
                </div>
              </div>

              {/* 带宽利用率进度条 */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <div className="flex items-center space-x-1">
                    <Activity className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">带宽利用率</span>
                  </div>
                  <span className="font-medium">{network.utilization}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${getUtilizationColor(network.utilization)}`}
                    style={{ width: `${network.utilization}%` }}
                  ></div>
                </div>
              </div>

              {/* 快速操作 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button className="text-pink-600 hover:text-pink-700 font-medium text-sm transition-colors">
                    网络配置
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    设备管理
                  </button>
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors">
                    流量分析
                  </button>
                </div>
                <button className="text-orange-600 hover:text-orange-700 font-medium text-sm transition-colors">
                  安全策略
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
