'use client'

import { useState, useEffect } from 'react'
import { FileText, Clock, CheckCircle, AlertCircle, Users } from 'lucide-react'

interface ServiceData {
  totalServices: number
  onlineServices: number
  todayApplications: number
  processingTime: number
  satisfactionRate: number
  popularServices: Array<{
    name: string
    applications: number
    trend: string
  }>
  recentActivity: Array<{
    time: string
    action: string
    status: 'completed' | 'processing' | 'pending'
  }>
}

export default function ServiceModule() {
  const [data, setData] = useState<ServiceData>({
    totalServices: 0,
    onlineServices: 0,
    todayApplications: 0,
    processingTime: 0,
    satisfactionRate: 0,
    popularServices: [],
    recentActivity: []
  })

  useEffect(() => {
    const loadData = () => {
      setData({
        totalServices: 156,
        onlineServices: 142,
        todayApplications: 1247,
        processingTime: 2.3,
        satisfactionRate: 98.5,
        popularServices: [
          { name: '营业执照办理', applications: 234, trend: '+12%' },
          { name: '户籍迁移', applications: 189, trend: '+8%' },
          { name: '社保查询', applications: 156, trend: '+15%' },
          { name: '税务申报', applications: 134, trend: '+5%' },
          { name: '公积金提取', applications: 98, trend: '+22%' }
        ],
        recentActivity: [
          { time: '14:32', action: '营业执照审批完成', status: 'completed' },
          { time: '14:28', action: '户籍迁移申请提交', status: 'processing' },
          { time: '14:25', action: '社保变更审核中', status: 'processing' },
          { time: '14:20', action: '税务登记已完成', status: 'completed' },
          { time: '14:15', action: '公积金申请待审核', status: 'pending' }
        ]
      })
    }

    loadData()
    const interval = setInterval(loadData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-400" />
      case 'processing':
        return <Clock className="w-3 h-3 text-yellow-400" />
      case 'pending':
        return <AlertCircle className="w-3 h-3 text-orange-400" />
      default:
        return null
    }
  }

  return (
    <div className="h-full bg-gradient-to-br from-purple-800/20 to-purple-900/20 backdrop-blur-sm rounded-2xl border border-purple-700/30 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <FileText className="w-5 h-5 text-purple-400" />
        <h3 className="text-lg font-bold text-white">政务服务</h3>
      </div>

      <div className="space-y-4 h-[calc(100%-60px)] overflow-y-auto">
        {/* 服务概览 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-800/30 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-white">{data.totalServices}</div>
            <div className="text-xs text-gray-400">总服务数</div>
            <div className="text-xs text-purple-400">{data.onlineServices}项在线</div>
          </div>
          
          <div className="bg-gray-800/30 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-white">{data.todayApplications}</div>
            <div className="text-xs text-gray-400">今日申请</div>
            <div className="text-xs text-green-400">+15%</div>
          </div>
        </div>

        {/* 服务指标 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-purple-300 mb-3">服务指标</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-300">平均办理时长</span>
              <span className="text-sm font-bold text-white">{data.processingTime}天</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-300">满意度</span>
              <span className="text-sm font-bold text-green-400">{data.satisfactionRate}%</span>
            </div>
          </div>
        </div>

        {/* 热门服务 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-purple-300 mb-3">热门服务</h4>
          <div className="space-y-2">
            {data.popularServices.slice(0, 4).map((service, index) => (
              <div key={service.name} className="flex items-center justify-between text-xs">
                <span className="text-gray-300 truncate flex-1">{service.name}</span>
                <div className="flex items-center space-x-2 ml-2">
                  <span className="text-purple-300">{service.applications}</span>
                  <span className="text-green-400">{service.trend}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 实时动态 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-purple-300 mb-3">实时动态</h4>
          <div className="space-y-2">
            {data.recentActivity.slice(0, 4).map((activity, index) => (
              <div key={index} className="flex items-center space-x-2 text-xs">
                <span className="text-gray-400 w-10">{activity.time}</span>
                {getStatusIcon(activity.status)}
                <span className="text-gray-300 truncate flex-1">{activity.action}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
