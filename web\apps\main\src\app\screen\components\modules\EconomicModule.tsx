'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, DollarSign, Building2, Briefcase } from 'lucide-react'

interface EconomicData {
  gdp: {
    current: number
    growth: number
  }
  revenue: {
    current: number
    growth: number
  }
  enterprises: {
    total: number
    newThisMonth: number
  }
  employment: {
    rate: number
    trend: number
  }
  industryDistribution: Array<{
    name: string
    percentage: number
    color: string
  }>
}

export default function EconomicModule() {
  const [data, setData] = useState<EconomicData>({
    gdp: { current: 0, growth: 0 },
    revenue: { current: 0, growth: 0 },
    enterprises: { total: 0, newThisMonth: 0 },
    employment: { rate: 0, trend: 0 },
    industryDistribution: []
  })

  useEffect(() => {
    const loadData = () => {
      setData({
        gdp: {
          current: 2456.8,
          growth: 8.5
        },
        revenue: {
          current: 345.6,
          growth: 12.3
        },
        enterprises: {
          total: 45623,
          newThisMonth: 234
        },
        employment: {
          rate: 96.8,
          trend: 1.2
        },
        industryDistribution: [
          { name: '制造业', percentage: 35.2, color: 'from-blue-500 to-blue-600' },
          { name: '服务业', percentage: 28.7, color: 'from-green-500 to-green-600' },
          { name: '科技业', percentage: 18.9, color: 'from-purple-500 to-purple-600' },
          { name: '农业', percentage: 10.4, color: 'from-yellow-500 to-yellow-600' },
          { name: '其他', percentage: 6.8, color: 'from-gray-500 to-gray-600' }
        ]
      })
    }

    loadData()
    const interval = setInterval(loadData, 60000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="h-full bg-gradient-to-br from-green-800/20 to-green-900/20 backdrop-blur-sm rounded-2xl border border-green-700/30 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <TrendingUp className="w-5 h-5 text-green-400" />
        <h3 className="text-lg font-bold text-white">经济指标</h3>
      </div>

      <div className="space-y-4 h-[calc(100%-60px)] overflow-y-auto">
        {/* 核心指标 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-800/30 rounded-lg p-3 text-center">
            <DollarSign className="w-6 h-6 text-green-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{data.gdp.current}亿</div>
            <div className="text-xs text-gray-400">GDP总量</div>
            <div className="text-xs text-green-400">+{data.gdp.growth}%</div>
          </div>
          
          <div className="bg-gray-800/30 rounded-lg p-3 text-center">
            <Building2 className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{data.revenue.current}亿</div>
            <div className="text-xs text-gray-400">财政收入</div>
            <div className="text-xs text-green-400">+{data.revenue.growth}%</div>
          </div>
        </div>

        {/* 企业统计 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-green-300 mb-3">企业统计</h4>
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-300">注册企业总数</span>
            <span className="text-sm font-bold text-white">{data.enterprises.total.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-300">本月新增</span>
            <span className="text-sm font-bold text-green-400">+{data.enterprises.newThisMonth}</span>
          </div>
        </div>

        {/* 就业率 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-green-300 mb-3">就业情况</h4>
          <div className="text-center">
            <div className="text-2xl font-bold text-white mb-1">{data.employment.rate}%</div>
            <div className="text-xs text-gray-400 mb-2">就业率</div>
            <div className="text-xs text-green-400">较上月 +{data.employment.trend}%</div>
          </div>
        </div>

        {/* 产业分布 */}
        <div className="bg-gray-800/30 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-green-300 mb-3">产业分布</h4>
          <div className="space-y-2">
            {data.industryDistribution.map((industry, index) => (
              <div key={industry.name} className="flex items-center justify-between">
                <span className="text-xs text-gray-300">{industry.name}</span>
                <div className="flex items-center space-x-2 flex-1 mx-2">
                  <div className="flex-1 bg-gray-700 rounded-full h-1.5">
                    <div 
                      className={`bg-gradient-to-r ${industry.color} h-1.5 rounded-full transition-all duration-1000`}
                      style={{ width: `${industry.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-green-300 w-8">{industry.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
